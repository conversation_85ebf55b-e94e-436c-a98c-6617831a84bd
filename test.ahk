; AutoHotkey v2 脚本
; 功能：在当前资源管理器目录下打开 Windows Terminal 并运行 crush 程序

#HotIf WinActive("ahk_class CabinetWClass") || WinActive("ahk_class ExploreWClass")
^+t:: ; Ctrl+Shift+T 快捷键
{
    ; 获取当前资源管理器的路径
    currentPath := GetExplorerPath()
    
    if (currentPath != "") {
        ; 获取显示器数量
        monitorCount := MonitorGetCount()
        targetMonitor := (monitorCount >= 2) ? 2 : 1
        
        ; 构建命令行参数 - 使用 cmd 来运行 crush
        terminalPath := "C:\tools\WindowsTerminal\WindowsTerminal.exe"
        cmdArgs := '-d "' . currentPath . '" -- cmd /k crush'
        
        ; 运行 Windows Terminal
        Run(terminalPath . " " . cmdArgs)
        
        ; 等待窗口出现并最大化
        if WinWait("ahk_exe WindowsTerminal.exe",, 3) {
            MonitorGetWorkArea(targetMonitor, &workLeft, &workTop, &workRight, &workBottom)
            WinMove(workLeft + 100, workTop + 100,,, "ahk_exe WindowsTerminal.exe")
            WinMaximize("ahk_exe WindowsTerminal.exe")
        }
    } else {
        MsgBox("无法获取当前目录路径", "错误", 0x10)
    }
}
#HotIf

; 获取资源管理器当前路径的函数
GetExplorerPath() {
    hwnd := WinExist("A")
    for window in ComObject("Shell.Application").Windows {
        if (window.hwnd = hwnd) {
            try {
                url := window.LocationURL
                ; 解码 URL 编码的路径
                path := UrlDecode(url)
                ; 移除 "file:///" 前缀
                path := RegExReplace(path, "^file:///", "")
                ; 将正斜杠替换为反斜杠
                path := StrReplace(path, "/", "\")
                return path
            }
        }
    }
    return ""
}

; URL 解码函数
UrlDecode(str) {
    Loop {
        if !RegExMatch(str, "%([0-9A-Fa-f]{2})", &match) {
            break
        }
        char := Chr("0x" . match[1])
        str := StrReplace(str, match[0], char)
    }
    return str
}

; 显示当前显示器信息（可选，用于调试）
^+i:: ; Ctrl+Shift+I 查看显示器信息
{
    info := "显示器数量: " . MonitorGetCount() . "`n`n"
    
    Loop MonitorGetCount() {
        MonitorGet(A_Index, &left, &top, &right, &bottom)
        MonitorGetWorkArea(A_Index, &wLeft, &wTop, &wRight, &wBottom)
        
        info .= "显示器 " . A_Index . ":`n"
        info .= "  完整区域: " . left . ", " . top . " 到 " . right . ", " . bottom . "`n"
        info .= "  工作区域: " . wLeft . ", " . wTop . " 到 " . wRight . ", " . wBottom . "`n"
        info .= "  分辨率: " . (right - left) . " x " . (bottom - top) . "`n`n"
    }
    
    MsgBox(info, "显示器信息")
}