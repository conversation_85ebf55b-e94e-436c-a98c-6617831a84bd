{"time":"2025-08-04T20:32:34.5008541+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-04T20:32:34.5146464+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-04T20:32:35.6718154+08:00","level":"INFO","msg":"OK   20250424200609_initial.sql (2.99ms)"}
{"time":"2025-08-04T20:32:35.6718154+08:00","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (0s)"}
{"time":"2025-08-04T20:32:35.6728149+08:00","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (0s)"}
{"time":"2025-08-04T20:32:35.6738164+08:00","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (1ms)"}
{"time":"2025-08-04T20:32:35.6738164+08:00","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-08-04T20:32:35.6738164+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-04T20:32:35.6776774+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-04T20:32:35.6776774+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-04T20:32:35.679678+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-04T20:32:35.679678+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-04T20:32:40.7101632+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-04T20:58:30.7587455+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-04T20:58:30.7717549+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-04T20:58:31.9140275+08:00","level":"INFO","msg":"goose: no migrations to run. current version: 20250627000000"}
{"time":"2025-08-04T20:58:31.9140275+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-04T20:58:31.9161642+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-04T20:58:31.9166905+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-04T20:58:31.9188328+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-04T20:58:31.9188328+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-04T20:58:35.2368802+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-04T20:58:35.2368802+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-04T20:58:35.2389481+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-04T20:58:35.2389752+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-04T20:58:36.5484041+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-04T21:07:19.7494265+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-04T21:07:19.7622389+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-04T21:07:20.8999451+08:00","level":"INFO","msg":"goose: no migrations to run. current version: 20250627000000"}
{"time":"2025-08-04T21:07:20.8999451+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-04T21:07:20.9019436+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-04T21:07:20.9029428+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-04T21:07:20.9049648+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-04T21:07:20.9049648+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-04T21:08:55.278525+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-04T21:08:55.2926003+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-04T21:08:56.4515101+08:00","level":"INFO","msg":"goose: no migrations to run. current version: 20250627000000"}
{"time":"2025-08-04T21:08:56.4515101+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-04T21:08:56.4545081+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-04T21:08:56.4545081+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-04T21:08:56.4565122+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-04T21:08:56.4565122+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-04T21:09:36.9586322+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-04T21:09:36.9727584+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-04T21:09:38.1066848+08:00","level":"INFO","msg":"goose: no migrations to run. current version: 20250627000000"}
{"time":"2025-08-04T21:09:38.1066848+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-04T21:09:38.1092219+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-04T21:09:38.1092219+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-04T21:09:38.1112201+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-04T21:09:38.1112201+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"coder"}
